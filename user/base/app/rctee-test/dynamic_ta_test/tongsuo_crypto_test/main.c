/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#include <tee_api_types.h>
#include <tee_api_defines.h>
#include <tee_ta_api.h>

#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>

// Tongsuo includes
#include <openssl/evp.h>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <openssl/aes.h>
#include <openssl/rsa.h>
#include <openssl/hmac.h>
#include <openssl/rand.h>
#include <openssl/err.h>

#define TLOG_TAG "tongsuo_crypto_test"

// Command IDs
#define CMD_TEST_BASIC_CRYPTO       1
#define CMD_TEST_HASH_ALGORITHMS    2
#define CMD_TEST_SYMMETRIC_CRYPTO   3
#define CMD_TEST_ASYMMETRIC_CRYPTO  4
#define CMD_TEST_HMAC              5
#define CMD_TEST_ALL_ALGORITHMS    6

// Helper function to print hex data
static void print_hex(const char* label, const unsigned char* data, size_t len)
{
    char hex_str[256];
    char* p = hex_str;
    size_t i;

    if (len > 64) len = 64; // Limit output length

    for (i = 0; i < len && (p - hex_str) < sizeof(hex_str) - 3; i++) {
        sprintf(p, "%02x", data[i]);
        p += 2;
    }
    *p = '\0';

    TLOGI("%s: %s", label, hex_str);
}

static int test_basic_crypto(void)
{
    TLOGI("Testing basic crypto functionality with Tongsuo...");

    // Simple test - just verify we can link with Tongsuo
    TLOGI("Tongsuo crypto library linked successfully!");

    return TEE_SUCCESS;
}

static int test_hash_algorithms(void)
{
    TLOGI("=== Testing Hash Algorithms ===");

    const char* test_data = "Hello, Tongsuo TEE!";
    size_t data_len = strlen(test_data);
    unsigned char hash[64]; // Large enough for any hash
    unsigned int hash_len;
    int ret = TEE_SUCCESS;

    // Test MD5
    TLOGI("Testing MD5...");
    if (MD5((const unsigned char*)test_data, data_len, hash) != NULL) {
        print_hex("MD5", hash, MD5_DIGEST_LENGTH);
        TLOGI("MD5 test: PASSED");
    } else {
        TLOGE("MD5 test: FAILED");
        ret = TEE_ERROR_GENERIC;
    }

    // Test SHA256
    TLOGI("Testing SHA256...");
    if (SHA256((const unsigned char*)test_data, data_len, hash) != NULL) {
        print_hex("SHA256", hash, SHA256_DIGEST_LENGTH);
        TLOGI("SHA256 test: PASSED");
    } else {
        TLOGE("SHA256 test: FAILED");
        ret = TEE_ERROR_GENERIC;
    }

    // Test SHA512
    TLOGI("Testing SHA512...");
    if (SHA512((const unsigned char*)test_data, data_len, hash) != NULL) {
        print_hex("SHA512", hash, SHA512_DIGEST_LENGTH);
        TLOGI("SHA512 test: PASSED");
    } else {
        TLOGE("SHA512 test: FAILED");
        ret = TEE_ERROR_GENERIC;
    }

    // Test using EVP interface
    TLOGI("Testing EVP hash interface...");
    EVP_MD_CTX* ctx = EVP_MD_CTX_new();
    if (ctx) {
        if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) == 1 &&
            EVP_DigestUpdate(ctx, test_data, data_len) == 1 &&
            EVP_DigestFinal_ex(ctx, hash, &hash_len) == 1) {
            print_hex("EVP SHA256", hash, hash_len);
            TLOGI("EVP hash test: PASSED");
        } else {
            TLOGE("EVP hash test: FAILED");
            ret = TEE_ERROR_GENERIC;
        }
        EVP_MD_CTX_free(ctx);
    } else {
        TLOGE("Failed to create EVP_MD_CTX");
        ret = TEE_ERROR_GENERIC;
    }

    return ret;
}

static int test_symmetric_crypto(void)
{
    TLOGI("=== Testing Symmetric Encryption ===");

    const char* plaintext = "This is a test message for symmetric encryption!";
    size_t plaintext_len = strlen(plaintext);
    unsigned char key[32] = {0}; // 256-bit key
    unsigned char iv[16] = {0};  // 128-bit IV
    unsigned char ciphertext[128];
    unsigned char decrypted[128];
    int ciphertext_len, decrypted_len;
    int ret = TEE_SUCCESS;

    // Initialize key and IV with some test values
    memset(key, 0x42, sizeof(key));
    memset(iv, 0x24, sizeof(iv));

    TLOGI("Testing AES-256-CBC encryption...");

    // Test AES encryption
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        TLOGE("Failed to create cipher context");
        return TEE_ERROR_GENERIC;
    }

    // Encrypt
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv) != 1) {
        TLOGE("AES encrypt init failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    if (EVP_EncryptUpdate(ctx, ciphertext, &ciphertext_len,
                         (const unsigned char*)plaintext, plaintext_len) != 1) {
        TLOGE("AES encrypt update failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    int final_len;
    if (EVP_EncryptFinal_ex(ctx, ciphertext + ciphertext_len, &final_len) != 1) {
        TLOGE("AES encrypt final failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }
    ciphertext_len += final_len;

    print_hex("AES Ciphertext", ciphertext, ciphertext_len);
    TLOGI("AES encryption: PASSED");

    // Decrypt
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv) != 1) {
        TLOGE("AES decrypt init failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    if (EVP_DecryptUpdate(ctx, decrypted, &decrypted_len,
                         ciphertext, ciphertext_len) != 1) {
        TLOGE("AES decrypt update failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    if (EVP_DecryptFinal_ex(ctx, decrypted + decrypted_len, &final_len) != 1) {
        TLOGE("AES decrypt final failed");
        EVP_CIPHER_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }
    decrypted_len += final_len;
    decrypted[decrypted_len] = '\0';

    TLOGI("Decrypted text: %s", (char*)decrypted);

    if (memcmp(plaintext, decrypted, plaintext_len) == 0) {
        TLOGI("AES decryption: PASSED");
    } else {
        TLOGE("AES decryption: FAILED - text mismatch");
        ret = TEE_ERROR_GENERIC;
    }

    EVP_CIPHER_CTX_free(ctx);
    return ret;
}

static int test_hmac(void)
{
    TLOGI("=== Testing HMAC ===");

    const char* message = "The quick brown fox jumps over the lazy dog";
    const char* key = "secret_key_for_hmac_test";
    unsigned char hmac_result[64];
    unsigned int hmac_len;
    int ret = TEE_SUCCESS;

    TLOGI("Testing HMAC-SHA256...");

    // Test HMAC-SHA256
    if (HMAC(EVP_sha256(), key, strlen(key),
             (const unsigned char*)message, strlen(message),
             hmac_result, &hmac_len) != NULL) {
        print_hex("HMAC-SHA256", hmac_result, hmac_len);
        TLOGI("HMAC-SHA256 test: PASSED");
    } else {
        TLOGE("HMAC-SHA256 test: FAILED");
        ret = TEE_ERROR_GENERIC;
    }

    // Test HMAC using EVP interface
    TLOGI("Testing HMAC with EVP interface...");
    EVP_PKEY* pkey = EVP_PKEY_new_mac_key(EVP_PKEY_HMAC, NULL,
                                          (const unsigned char*)key, strlen(key));
    if (pkey) {
        EVP_MD_CTX* ctx = EVP_MD_CTX_new();
        if (ctx) {
            if (EVP_DigestSignInit(ctx, NULL, EVP_sha256(), NULL, pkey) == 1 &&
                EVP_DigestSignUpdate(ctx, message, strlen(message)) == 1) {
                size_t sig_len = sizeof(hmac_result);
                if (EVP_DigestSignFinal(ctx, hmac_result, &sig_len) == 1) {
                    print_hex("EVP HMAC-SHA256", hmac_result, sig_len);
                    TLOGI("EVP HMAC test: PASSED");
                } else {
                    TLOGE("EVP HMAC final failed");
                    ret = TEE_ERROR_GENERIC;
                }
            } else {
                TLOGE("EVP HMAC init/update failed");
                ret = TEE_ERROR_GENERIC;
            }
            EVP_MD_CTX_free(ctx);
        } else {
            TLOGE("Failed to create EVP_MD_CTX for HMAC");
            ret = TEE_ERROR_GENERIC;
        }
        EVP_PKEY_free(pkey);
    } else {
        TLOGE("Failed to create HMAC key");
        ret = TEE_ERROR_GENERIC;
    }

    return ret;
}

static int test_asymmetric_crypto(void)
{
    TLOGI("=== Testing Asymmetric Crypto ===");

    // For now, just test RSA key generation to verify the library works
    TLOGI("Testing RSA key generation...");

    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, NULL);
    if (!ctx) {
        TLOGE("Failed to create RSA key context");
        return TEE_ERROR_GENERIC;
    }

    if (EVP_PKEY_keygen_init(ctx) <= 0) {
        TLOGE("Failed to init RSA key generation");
        EVP_PKEY_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 1024) <= 0) {
        TLOGE("Failed to set RSA key bits");
        EVP_PKEY_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    EVP_PKEY* pkey = NULL;
    if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
        TLOGE("RSA key generation failed");
        EVP_PKEY_CTX_free(ctx);
        return TEE_ERROR_GENERIC;
    }

    TLOGI("RSA key generation: PASSED");

    // Clean up
    EVP_PKEY_free(pkey);
    EVP_PKEY_CTX_free(ctx);

    return TEE_SUCCESS;
}

static int test_all_algorithms(void)
{
    TLOGI("=== Running All Tongsuo Algorithm Tests ===");
    int ret = TEE_SUCCESS;

    if (test_basic_crypto() != TEE_SUCCESS) {
        TLOGE("Basic crypto test failed");
        ret = TEE_ERROR_GENERIC;
    }

    if (test_hash_algorithms() != TEE_SUCCESS) {
        TLOGE("Hash algorithms test failed");
        ret = TEE_ERROR_GENERIC;
    }

    if (test_symmetric_crypto() != TEE_SUCCESS) {
        TLOGE("Symmetric crypto test failed");
        ret = TEE_ERROR_GENERIC;
    }

    if (test_hmac() != TEE_SUCCESS) {
        TLOGE("HMAC test failed");
        ret = TEE_ERROR_GENERIC;
    }

    if (test_asymmetric_crypto() != TEE_SUCCESS) {
        TLOGE("Asymmetric crypto test failed");
        ret = TEE_ERROR_GENERIC;
    }

    if (ret == TEE_SUCCESS) {
        TLOGI("=== ALL TESTS PASSED! ===");
    } else {
        TLOGE("=== SOME TESTS FAILED! ===");
    }

    return ret;
}

/* RCTEE回调函数实现 */
int RCTEE_OnCall(uint32_t cmd,
                 uint8_t* in_buf,
                 size_t in_buf_size,
                 uint8_t** out_buf,
                 size_t* out_buf_size) {
    (void)in_buf;
    (void)in_buf_size;
    (void)out_buf;
    (void)out_buf_size;

    TLOGI("Tongsuo Crypto Test TA - Command ID: %u", cmd);

    switch (cmd) {
    case CMD_TEST_BASIC_CRYPTO:
        return test_basic_crypto();

    case CMD_TEST_HASH_ALGORITHMS:
        return test_hash_algorithms();

    case CMD_TEST_SYMMETRIC_CRYPTO:
        return test_symmetric_crypto();

    case CMD_TEST_ASYMMETRIC_CRYPTO:
        return test_asymmetric_crypto();

    case CMD_TEST_HMAC:
        return test_hmac();

    case CMD_TEST_ALL_ALGORITHMS:
        return test_all_algorithms();

    default:
        TLOGE("Unknown command ID: %u", cmd);
        return ERROR_INVALID;
    }
}

int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo Crypto Test TA - Client connected");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    (void)cookie;
    TLOGI("Tongsuo Crypto Test TA - Client disconnected");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo Crypto Test TA - Initialized");
    return TEE_SUCCESS;
}
