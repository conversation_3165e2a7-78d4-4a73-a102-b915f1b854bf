out/build-imx8mp/user_product/lib/boringssl/opensource_libs/boringssl/src/crypto/bio/hexdump.o: \
  kernel/rctee/lib/ubsan/exemptlist \
  /home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt \
  opensource_libs/boringssl/src/crypto/bio/hexdump.c \
  opensource_libs/Tongsuo/include/openssl/bio.h \
  opensource_libs/Tongsuo/include/openssl/macros.h \
  opensource_libs/Tongsuo/include/openssl/opensslconf.h \
  opensource_libs/Tongsuo/include/openssl/configuration.h \
  opensource_libs/Tongsuo/include/openssl/symbol_prefix.h \
  opensource_libs/Tongsuo/include/openssl/opensslv.h \
  opensource_libs/Tongsuo/include/openssl/e_os2.h \
  opensource_libs/libcxx/include/inttypes.h \
  opensource_libs/libcxx/include/__config \
  opensource_libs/musl/include/inttypes.h \
  opensource_libs/musl/include/features.h \
  opensource_libs/libcxx/include/stdint.h \
  opensource_libs/musl/include/stdint.h \
  opensource_libs/musl/arch/aarch64/bits/alltypes.h \
  opensource_libs/musl/arch/aarch64/bits/stdint.h \
  opensource_libs/musl/include/stdarg.h \
  opensource_libs/Tongsuo/include/openssl/crypto.h \
  opensource_libs/libcxx/include/stdlib.h \
  opensource_libs/musl/include/stdlib.h \
  opensource_libs/musl/include/alloca.h \
  opensource_libs/musl/include/time.h \
  opensource_libs/Tongsuo/include/openssl/safestack.h \
  opensource_libs/Tongsuo/include/openssl/stack.h \
  opensource_libs/Tongsuo/include/openssl/types.h \
  opensource_libs/libcxx/include/limits.h \
  opensource_libs/musl/include/limits.h \
  opensource_libs/musl/arch/aarch64/bits/limits.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr.h \
  opensource_libs/Tongsuo/include/openssl/symhacks.h \
  opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h \
  opensource_libs/Tongsuo/include/openssl/core.h \
  opensource_libs/libcxx/include/stddef.h \
  opensource_libs/musl/include/stddef.h \
  opensource_libs/musl/include/syslog.h \
  opensource_libs/Tongsuo/include/openssl/bioerr.h \
  opensource_libs/libcxx/include/string.h \
  opensource_libs/musl/include/string.h \
  opensource_libs/musl/include/strings.h \
  opensource_libs/boringssl/src/crypto/bio/../internal.h \
  opensource_libs/boringssl/src/include/openssl/arm_arch.h \
  opensource_libs/boringssl/src/include/openssl/target.h \
  opensource_libs/boringssl/src/include/openssl/ex_data.h \
  opensource_libs/boringssl/src/include/openssl/base.h \
  opensource_libs/musl/include/sys/types.h \
  opensource_libs/musl/include/endian.h \
  opensource_libs/musl/include/sys/select.h \
  opensource_libs/boringssl/src/include/openssl/is_boringssl.h \
  opensource_libs/boringssl/src/include/openssl/thread.h \
  opensource_libs/musl/include/assert.h \
  opensource_libs/musl/include/stdalign.h

kernel/rctee/lib/ubsan/exemptlist:

/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b/lib/clang/16.0.2/share/cfi_ignorelist.txt:

opensource_libs/Tongsuo/include/openssl/bio.h:

opensource_libs/Tongsuo/include/openssl/macros.h:

opensource_libs/Tongsuo/include/openssl/opensslconf.h:

opensource_libs/Tongsuo/include/openssl/configuration.h:

opensource_libs/Tongsuo/include/openssl/symbol_prefix.h:

opensource_libs/Tongsuo/include/openssl/opensslv.h:

opensource_libs/Tongsuo/include/openssl/e_os2.h:

opensource_libs/libcxx/include/inttypes.h:

opensource_libs/libcxx/include/__config:

opensource_libs/musl/include/inttypes.h:

opensource_libs/musl/include/features.h:

opensource_libs/libcxx/include/stdint.h:

opensource_libs/musl/include/stdint.h:

opensource_libs/musl/arch/aarch64/bits/alltypes.h:

opensource_libs/musl/arch/aarch64/bits/stdint.h:

opensource_libs/musl/include/stdarg.h:

opensource_libs/Tongsuo/include/openssl/crypto.h:

opensource_libs/libcxx/include/stdlib.h:

opensource_libs/musl/include/stdlib.h:

opensource_libs/musl/include/alloca.h:

opensource_libs/musl/include/time.h:

opensource_libs/Tongsuo/include/openssl/safestack.h:

opensource_libs/Tongsuo/include/openssl/stack.h:

opensource_libs/Tongsuo/include/openssl/types.h:

opensource_libs/libcxx/include/limits.h:

opensource_libs/musl/include/limits.h:

opensource_libs/musl/arch/aarch64/bits/limits.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr.h:

opensource_libs/Tongsuo/include/openssl/symhacks.h:

opensource_libs/Tongsuo/include/openssl/cryptoerr_legacy.h:

opensource_libs/Tongsuo/include/openssl/core.h:

opensource_libs/libcxx/include/stddef.h:

opensource_libs/musl/include/stddef.h:

opensource_libs/musl/include/syslog.h:

opensource_libs/Tongsuo/include/openssl/bioerr.h:

opensource_libs/libcxx/include/string.h:

opensource_libs/musl/include/string.h:

opensource_libs/musl/include/strings.h:

opensource_libs/boringssl/src/crypto/bio/../internal.h:

opensource_libs/boringssl/src/include/openssl/arm_arch.h:

opensource_libs/boringssl/src/include/openssl/target.h:

opensource_libs/boringssl/src/include/openssl/ex_data.h:

opensource_libs/boringssl/src/include/openssl/base.h:

opensource_libs/musl/include/sys/types.h:

opensource_libs/musl/include/endian.h:

opensource_libs/musl/include/sys/select.h:

opensource_libs/boringssl/src/include/openssl/is_boringssl.h:

opensource_libs/boringssl/src/include/openssl/thread.h:

opensource_libs/musl/include/assert.h:

opensource_libs/musl/include/stdalign.h:
